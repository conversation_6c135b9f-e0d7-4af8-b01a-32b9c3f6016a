'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Loader2,
} from 'lucide-react';
import { ComposioApp } from '@/types/composio';
import { getComposioAppIcon } from '@/lib/icon-mapping';

interface MCPAppsModalProps {
  isOpen: boolean;
  onClose: () => void;
  apps: ComposioApp[];
  connectedApps: Set<string>;
  connectingApps: Set<string>;
  disconnectingApps: Set<string>;
  onConnect: (appKey: string, appName: string) => void;
  onDisconnect: (appKey: string, appName: string) => void;
  onViewTools?: (appKey: string, appName: string) => void;
  isLoading?: boolean;
}

export function MCPAppsModal({
  isOpen,
  onClose,
  apps,
  connectedApps,
  connectingApps,
  disconnectingApps,
  onConnect,
  onDisconnect,
  onViewTools,
  isLoading = false,
}: MCPAppsModalProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredApps, setFilteredApps] = useState<ComposioApp[]>([]);

  // Filter and sort apps based on search query and connection status
  useEffect(() => {
    let appsToFilter = apps;

    // Filter by search query if present
    if (searchQuery.trim()) {
      appsToFilter = apps.filter(app =>
        app.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Sort so connected apps appear first
    const sortedApps = appsToFilter.sort((a, b) => {
      const aConnected = connectedApps.has(a.key);
      const bConnected = connectedApps.has(b.key);

      // Connected apps first
      if (aConnected && !bConnected) return -1;
      if (!aConnected && bConnected) return 1;

      // If both have same connection status, maintain original order
      return 0;
    });

    setFilteredApps(sortedApps);
  }, [searchQuery, apps, connectedApps]);

  const handleConnect = (appKey: string, appName: string) => {
    onConnect(appKey, appName);
  };

  const handleDisconnect = (appKey: string, appName: string) => {
    onDisconnect(appKey, appName);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg max-h-[85vh] p-0 overflow-hidden">
        <DialogHeader className="px-6 pt-6 pb-4 border-b border-border/50">
          <DialogTitle className="text-lg font-semibold text-foreground">
            All Apps
          </DialogTitle>
        </DialogHeader>

        {/* Apps List */}
        <ScrollArea className="max-h-[calc(85vh-120px)]">
          <div className="px-6 pt-4 pb-6">
            {isLoading ? (
              <div className="space-y-2">
                {Array.from({ length: 8 }).map((_, i) => (
                  <Skeleton key={i} className="h-16 rounded-lg" />
                ))}
              </div>
            ) : (
              <div className="space-y-2">
                {filteredApps.map((app) => (
                  <MCPAppRow
                    key={app.key}
                    app={app}
                    isConnected={connectedApps.has(app.key)}
                    isConnecting={connectingApps.has(app.key)}
                    isDisconnecting={disconnectingApps.has(app.key)}
                    onConnect={() => handleConnect(app.key, app.name)}
                    onDisconnect={() => handleDisconnect(app.key, app.name)}
                  />
                ))}
              </div>
            )}

            {/* No results message */}
            {!isLoading && filteredApps.length === 0 && (
              <div className="text-center py-8">
                <p className="text-muted-foreground text-sm">
                  No apps available
                </p>
              </div>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}

// Individual app row component for the modal
interface MCPAppRowProps {
  app: ComposioApp;
  isConnected: boolean;
  isConnecting: boolean;
  isDisconnecting: boolean;
  onConnect: () => void;
  onDisconnect: () => void;
}

function MCPAppRow({
  app,
  isConnected,
  isConnecting,
  isDisconnecting,
  onConnect,
  onDisconnect,
}: MCPAppRowProps) {
  const IconComponent = getComposioAppIcon(app);
  const isLoading = isConnecting || isDisconnecting;

  const handleClick = () => {
    if (isLoading) return;

    if (isConnected) {
      onDisconnect();
    } else {
      onConnect();
    }
  };

  return (
    <div className="flex items-center justify-between py-4 px-4 hover:bg-muted/40 rounded-lg transition-all duration-200 group border border-transparent hover:border-border/50">
      {/* Left: Icon + Name */}
      <div className="flex items-center gap-4 min-w-0 flex-1">
        {/* App Icon */}
        <div className="flex-shrink-0 w-10 h-10 rounded-lg overflow-hidden bg-background border border-border/20 flex items-center justify-center">
          <IconComponent className="w-6 h-6" />
        </div>

        {/* App Name */}
        <div className="min-w-0 flex-1">
          <div className="font-medium text-sm text-foreground truncate">
            {app.name}
          </div>
        </div>
      </div>

      {/* Right: Status Badge */}
      <div className="flex items-center flex-shrink-0 ml-4">
        {isLoading ? (
          <div className="flex items-center gap-2 px-3 py-1.5 rounded-md bg-muted/50 text-xs text-muted-foreground">
            <Loader2 className="h-3 w-3 animate-spin" />
            {isConnecting ? 'Connecting...' : 'Disconnecting...'}
          </div>
        ) : isConnected ? (
          <Button
            variant="outline"
            size="sm"
            onClick={handleClick}
            className="h-8 px-3 text-xs text-red-600 border-red-200 hover:text-red-700 hover:bg-red-50 hover:border-red-300 dark:text-red-400 dark:border-red-800 dark:hover:text-red-300 dark:hover:bg-red-950/20 dark:hover:border-red-700 transition-colors"
          >
            Disconnect
          </Button>
        ) : (
          <Button
            variant="outline"
            size="sm"
            onClick={handleClick}
            className="h-8 px-3 text-xs text-blue-600 border-blue-200 hover:text-blue-700 hover:bg-blue-50 hover:border-blue-300 dark:text-blue-400 dark:border-blue-800 dark:hover:text-blue-300 dark:hover:bg-blue-950/20 dark:hover:border-blue-700 transition-colors"
          >
            Connect
          </Button>
        )}
      </div>
    </div>
  );
}
