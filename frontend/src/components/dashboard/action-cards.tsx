'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Workflow, Play, Plus, ArrowRight } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useFeatureFlag } from '@/lib/feature-flags';

interface ActionCardsProps {
  className?: string;
}

interface ActionCardProps {
  icon: React.ReactNode;
  title: string;
  subtitle?: string;
  onClick: () => void;
  actionIcon?: React.ReactNode;
  width?: string;
}

function ActionCard({ icon, title, subtitle, onClick, actionIcon, width = "w-52" }: ActionCardProps) {
  return (
    <motion.div
      whileHover={{
        transition: { duration: 0.2, ease: "easeOut" }
      }}
      className="flex-shrink-0"
    >
      <Card
        className={`${width} h-14 cursor-pointer transition-all duration-200 hover:shadow-lg bg-muted/50 dark:bg-muted/30 border border-border hover:border-border/80`}
        onClick={onClick}
      >
        <CardContent className="px-4 h-full flex items-center justify-between">
          {/* Left: Icon + Text */}
          <div className="flex items-center gap-3 min-w-0 flex-1">
            {/* Action Icon */}
            <div className="w-7 h-7 flex-shrink-0 flex items-center justify-center bg-primary/10 border border-primary/20 rounded-md">
              {icon}
            </div>

            {/* Text Content */}
            <div className="min-w-0 flex-1">
              <div className="font-medium text-sm text-foreground leading-tight">
                {title}
              </div>
              {subtitle && (
                <div className="text-xs text-muted-foreground">
                  {subtitle}
                </div>
              )}
            </div>
          </div>

          {/* Right: Action Button */}
          <div className="flex items-center flex-shrink-0 ml-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onClick();
              }}
              className="h-7 w-7 p-0 rounded-full hover:bg-primary/10 group"
            >
              {actionIcon || <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />}
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

export function ActionCards({ className }: ActionCardsProps) {
  const router = useRouter();
  const { enabled: workflowsEnabled } = useFeatureFlag("workflows");

  const handleCreateWorkflow = () => {
    router.push('/workflows');
  };

  const handleLearnPlatform = () => {
    // TODO: Replace with actual YouTube link when provided
    window.open('https://www.youtube.com/watch?v=dQw4w9WgXcQ', '_blank');
  };

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex justify-between items-center mb-4">
        <span className="text-sm text-muted-foreground">
          quick actions
        </span>
      </div>

      {/* Cards container */}
      <div className="flex gap-4 overflow-x-auto scrollbar-hide">
        {/* Workflow Creation Card - Only show if workflows feature flag is enabled */}
        {workflowsEnabled && (
          <ActionCard
            icon={<Workflow className="h-5 w-5 text-primary" />}
            title="Create workflow"
            onClick={handleCreateWorkflow}
            actionIcon={<Plus className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />}
            width="w-60"
          />
        )}

        {/* Learning Resources Card */}
        <ActionCard
          icon={<Play className="h-5 w-5 text-primary" />}
          title="Learn how to use our platform"
          onClick={handleLearnPlatform}
          width="w-80"
        />
      </div>
    </div>
  );
}
